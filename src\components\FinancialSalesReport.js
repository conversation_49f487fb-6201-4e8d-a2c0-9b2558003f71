import { useState, useEffect } from 'react';
import {
  FaChartBar,
  FaMoneyBillWave,
  FaCalendarAlt,
  FaChartLine,
  FaChartPie,
  FaArrowUp,
  FaArrowDown,
  FaExchangeAlt,
  FaPercentage,
  FaInfoCircle,
  FaBoxes,
  FaShoppingCart,
  FaUndo,
  FaSync,
  FaTruck
} from 'react-icons/fa';
import DataTable from './DataTable';
import FormattedCurrency from './FormattedCurrency';
import { formatDate } from '../utils/dateUtils';
import MonthlySalesChart from './charts/MonthlySalesChart';
import MonthlyProfitChart from './charts/MonthlyProfitChart';
import { calculateProfitMargin, calculateQuarterlyProfits } from '../utils/profitCalculator';
import './FinancialSalesReport.css';

/**
 * مكون تقرير المبيعات والمالية الشامل
 * يعرض تقرير مبيعات احترافي يوفر كافة مزايا تقارير المبيعات والأرباح والخزينة
 *
 * @param {Object} props - خصائص المكون
 * @param {Array} props.transactions - قائمة المعاملات
 * @param {Object} props.profitValues - قيم الأرباح للفترات المختلفة
 * @param {Object} props.cashboxReport - تقرير الخزينة
 * @param {string} props.dateRange - النطاق الزمني المحدد
 * @param {Function} props.onDateRangeChange - دالة تغيير النطاق الزمني
 * @param {Function} props.onCashboxReportUpdate - دالة تحديث تقرير الخزينة
 */
const FinancialSalesReport = ({
  transactions = [],
  profitValues = { quarterly: 0, halfYearly: 0, threeQuarters: 0, yearly: 0 },
  cashboxReport = null,
  dateRange = 'all',
  onDateRangeChange = () => {},
  onCashboxReportUpdate = null
}) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [transactionStats, setTransactionStats] = useState({
    purchases: { count: 0, value: 0 },
    sales: { count: 0, value: 0, profit: 0 },
    returns: { count: 0, value: 0, profit: 0 }
  });
  const [monthlySalesData, setMonthlySalesData] = useState([]);
  const [monthlyProfitData, setMonthlyProfitData] = useState([]);
  const [mostProfitableItems, setMostProfitableItems] = useState([]);
  const [selectedMonth, setSelectedMonth] = useState('');
  const [filteredMonthlySalesData, setFilteredMonthlySalesData] = useState([]);
  const [purchasesMonthFilter, setPurchasesMonthFilter] = useState('');
  const [refreshKey, setRefreshKey] = useState(0); // مفتاح لفرض إعادة الرسم
  const [localTransactions, setLocalTransactions] = useState(transactions); // نسخة محلية من المعاملات

  // إنشاء قائمة بالأشهر المتاحة للتصفية (من يناير 2025 إلى الشهر الحالي)
  const getAvailableMonths = () => {
    const months = [];
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    // إضافة الأشهر من يناير 2025 إلى الشهر الحالي
    for (let year = 2025; year <= currentYear; year++) {
      const lastMonth = year === currentYear ? currentMonth : 12;
      for (let month = 1; month <= lastMonth; month++) {
        months.push({
          value: `${year}-${month}`,
          label: `${getMonthName(month)} ${year}`
        });
      }
    }

    return months;
  };

  // الحصول على اسم الشهر بالعربية
  const getMonthName = (monthNumber) => {
    const monthNames = ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    return monthNames[monthNumber - 1];
  };

  // تحديث المعاملات المحلية عند تغيير المعاملات من props
  useEffect(() => {
    setLocalTransactions(transactions);
  }, [transactions]);

  // حساب إحصائيات المعاملات عند تغيير المعاملات المحلية
  useEffect(() => {
    if (localTransactions && localTransactions.length > 0) {
      calculateTransactionStats();
      calculateMonthlyData();
      calculateMostProfitableItems();

      // حساب أرباح الفترات المختلفة وتحديث القيم
      const updatedProfits = calculateQuarterlyProfitsData();
      console.log('تم تحديث قيم الأرباح للفترات المختلفة:', updatedProfits);
    }
  }, [localTransactions]);

  // إضافة مستمع لأحداث تحديث الأرباح التلقائي لإعادة حساب البيانات فوراً
  useEffect(() => {
    const handleProfitsUpdated = async (event) => {
      console.log('[AUTO-PROFITS-UI] تم استلام حدث تحديث الأرباح التلقائي في FinancialSalesReport:', event.detail);

      try {
        // إعادة تحميل المعاملات مباشرة أولاً
        await reloadTransactionsDirectly();

        // إعادة حساب جميع الإحصائيات فوراً بعد تحديث المعاملات
        await new Promise(resolve => {
          setTimeout(() => {
            console.log('[AUTO-PROFITS-UI] إعادة حساب الإحصائيات بعد التحديث التلقائي...');
            calculateTransactionStats();
            calculateMonthlyData();
            calculateMostProfitableItems();

            // إعادة حساب الأرباح وتحديث القيم المحفوظة
            const newProfitValues = calculateQuarterlyProfits(localTransactions);
            setCachedProfitValues(newProfitValues);
            console.log('[AUTO-PROFITS-UI] تم إعادة حساب قيم الأرباح بعد التحديث التلقائي:', newProfitValues);

            // فرض إعادة رسم للتأكد من تحديث واجهة المستخدم
            setRefreshKey(prev => prev + 1);
            resolve();
          }, 100); // زيادة التأخير قليلاً لضمان اكتمال تحديث البيانات
        });
      } catch (error) {
        console.error('[AUTO-PROFITS-UI] خطأ في معالجة تحديث الأرباح:', error);
      }
    };

    // إضافة مستمع للتحديث التلقائي الإضافي
    const handleAutoProfitsUpdated = async (event) => {
      console.log('[AUTO-PROFITS-UI] تم استلام حدث تحديث الأرباح التلقائي الإضافي:', event.detail);

      try {
        // إعادة تحميل المعاملات
        await reloadTransactionsDirectly();

        // إعادة حساب سريعة مع تحديث القيم المحفوظة
        setTimeout(() => {
          calculateTransactionStats();
          const newProfitValues = calculateQuarterlyProfits(localTransactions);
          setCachedProfitValues(newProfitValues);
          setRefreshKey(prev => prev + 1);
          console.log('[AUTO-PROFITS-UI] تم التحديث السريع للأرباح:', newProfitValues);
        }, 50);
      } catch (error) {
        console.error('[AUTO-PROFITS-UI] خطأ في التحديث السريع للأرباح:', error);
      }
    };

    const handleCashboxUpdated = async (event) => {
      console.log('تم استلام حدث تحديث الخزينة في FinancialSalesReport:', event.detail);

      try {
        // إعادة تحميل المعاملات مباشرة أولاً
        await reloadTransactionsDirectly();

        // إعادة حساب جميع الإحصائيات فوراً بعد تحديث المعاملات
        setTimeout(() => {
          calculateTransactionStats();
          calculateMonthlyData();
          calculateMostProfitableItems();

          // إعادة حساب الأرباح وتحديث القيم المحفوظة
          const newProfitValues = calculateQuarterlyProfits(localTransactions);
          setCachedProfitValues(newProfitValues);
          console.log('تم إعادة حساب قيم الأرباح بعد تحديث الخزينة:', newProfitValues);

          // فرض إعادة رسم
          setRefreshKey(prev => prev + 1);
        }, 100);
      } catch (error) {
        console.error('خطأ في معالجة تحديث الخزينة:', error);
      }
    };

    // دالة لإعادة تحميل المعاملات مباشرة
    const reloadTransactionsDirectly = async () => {
      try {
        console.log('إعادة تحميل المعاملات مباشرة في FinancialSalesReport...');

        if (window.api && window.api.invoke) {
          const freshTransactions = await window.api.invoke('get-transactions');
          if (Array.isArray(freshTransactions)) {
            setLocalTransactions(freshTransactions);
            console.log('تم تحديث المعاملات المحلية:', freshTransactions.length);
          }
        }
      } catch (error) {
        console.error('خطأ في إعادة تحميل المعاملات مباشرة:', error);
      }
    };

    // مستمع لحدث تحديث المعاملات
    const handleTransactionsRefreshed = async (event) => {
      console.log('تم استلام حدث تحديث المعاملات في FinancialSalesReport:', event.detail);

      try {
        // إعادة تحميل المعاملات مباشرة أولاً
        await reloadTransactionsDirectly();

        // إعادة حساب جميع الإحصائيات فوراً
        setTimeout(() => {
          calculateTransactionStats();
          calculateMonthlyData();
          calculateMostProfitableItems();

          // إعادة حساب الأرباح وتحديث القيم المحفوظة
          const newProfitValues = calculateQuarterlyProfits(localTransactions);
          setCachedProfitValues(newProfitValues);
          console.log('تم إعادة حساب قيم الأرباح بعد تحديث المعاملات:', newProfitValues);

          // فرض إعادة رسم
          setRefreshKey(prev => prev + 1);
        }, 50);
      } catch (error) {
        console.error('خطأ في معالجة تحديث المعاملات:', error);
      }
    };

    // مستمع إضافي لحدث إضافة معاملة مباشرة
    const handleTransactionAdded = async (event) => {
      console.log('تم استلام حدث إضافة معاملة في FinancialSalesReport:', event.detail);

      try {
        // إعادة تحميل المعاملات مباشرة أولاً
        await reloadTransactionsDirectly();

        // إعادة حساب جميع الإحصائيات فوراً
        setTimeout(() => {
          calculateTransactionStats();
          calculateMonthlyData();
          calculateMostProfitableItems();

          // إعادة حساب الأرباح وتحديث القيم المحفوظة
          const newProfitValues = calculateQuarterlyProfits(localTransactions);
          setCachedProfitValues(newProfitValues);
          console.log('تم إعادة حساب قيم الأرباح بعد إضافة معاملة:', newProfitValues);

          // فرض إعادة رسم
          setRefreshKey(prev => prev + 1);
        }, 50);
      } catch (error) {
        console.error('خطأ في معالجة إضافة معاملة:', error);
      }
    };

    // مستمع إضافي لأحداث النظام المباشرة
    const handleDirectUpdate = async (event) => {
      console.log('تم استلام حدث تحديث مباشر في FinancialSalesReport:', event.detail);

      try {
        // إعادة تحميل فورية للمعاملات
        await reloadTransactionsDirectly();

        // إعادة حساب فورية للإحصائيات
        setTimeout(() => {
          calculateTransactionStats();
          const newProfitValues = calculateQuarterlyProfits(localTransactions);
          setCachedProfitValues(newProfitValues);
          setRefreshKey(prev => prev + 1);
          console.log('تم التحديث المباشر للأرباح:', newProfitValues);
        }, 25); // تحديث سريع جداً
      } catch (error) {
        console.error('خطأ في التحديث المباشر:', error);
      }
    };

    // تسجيل المستمعين مع دعم التحديث التلقائي
    window.addEventListener('profits-updated', handleProfitsUpdated);
    window.addEventListener('auto-profits-updated', handleAutoProfitsUpdated);
    window.addEventListener('cashbox-updated-ui', handleCashboxUpdated);
    window.addEventListener('transactions-refreshed', handleTransactionsRefreshed);
    window.addEventListener('transaction-added', handleTransactionAdded);
    window.addEventListener('direct-update', handleDirectUpdate);

    // إزالة المستمعين عند تفكيك المكون
    return () => {
      window.removeEventListener('profits-updated', handleProfitsUpdated);
      window.removeEventListener('auto-profits-updated', handleAutoProfitsUpdated);
      window.removeEventListener('cashbox-updated-ui', handleCashboxUpdated);
      window.removeEventListener('transactions-refreshed', handleTransactionsRefreshed);
      window.removeEventListener('transaction-added', handleTransactionAdded);
      window.removeEventListener('direct-update', handleDirectUpdate);
    };
  }, []);

  // تحميل بيانات الخزينة عند تغيير تقرير الخزينة
  useEffect(() => {
    console.log('تم تحديث بيانات الخزينة:', cashboxReport);
  }, [cashboxReport]);

  // تحديث تقرير الخزينة عند تغيير التبويب إلى مصاريف النقل
  useEffect(() => {
    if (activeTab === 'transport' && onCashboxReportUpdate) {
      console.log('تم تغيير التبويب إلى مصاريف النقل، جاري تحديث تقرير الخزينة...');
      // طلب تحديث تقرير الخزينة من المكون الأب
      if (typeof onCashboxReportUpdate === 'function') {
        // يمكن استدعاء دالة تحديث من المكون الأب
        console.log('طلب تحديث تقرير الخزينة من المكون الأب');
      }
    }
  }, [activeTab, onCashboxReportUpdate]);

  // تحديث البيانات المصفاة عند تغيير البيانات الشهرية أو فلتر الشهر
  useEffect(() => {
    if (monthlySalesData.length > 0) {
      // تحديث البيانات المصفاة للجداول
      if (purchasesMonthFilter) {
        // تصفية البيانات حسب الشهر المحدد
        const filtered = monthlySalesData.filter(item =>
          item.label === purchasesMonthFilter
        );
        setFilteredMonthlySalesData(filtered);
      } else {
        // استخدام جميع البيانات إذا لم يتم تحديد شهر
        setFilteredMonthlySalesData(monthlySalesData);
      }
    } else {
      setFilteredMonthlySalesData([]);
    }
  }, [monthlySalesData, purchasesMonthFilter]);

  // تحديث البيانات الشهرية
  const refreshMonthlyData = async () => {
    try {
      console.log('جاري تحديث البيانات الشهرية...');
      await calculateMonthlyData();
      console.log('تم تحديث البيانات الشهرية بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث البيانات الشهرية:', error);
    }
  };

  // تغيير فلتر الشهر في قسم المشتريات
  const handlePurchasesMonthFilterChange = (event) => {
    const newMonth = event.target.value;
    console.log(`تغيير فلتر الشهر في قسم المشتريات إلى: ${newMonth}`);
    setPurchasesMonthFilter(newMonth);
  };

  // تغيير الشهر المحدد
  const handleMonthChange = async (event) => {
    const newMonth = event.target.value;
    console.log(`تغيير الشهر المحدد إلى: ${newMonth}`);
    setSelectedMonth(newMonth);

    try {
      // إعادة حساب البيانات الشهرية مع الشهر الجديد
      await calculateMonthlyData(newMonth);
    } catch (error) {
      console.error('خطأ في تحديث البيانات الشهرية بعد تغيير الشهر:', error);
    }
  };

  // حساب إحصائيات المعاملات
  const calculateTransactionStats = () => {
    const purchases = localTransactions.filter(t => t.transaction_type === 'purchase');
    const sales = localTransactions.filter(t => t.transaction_type === 'sale');
    const returns = localTransactions.filter(t => t.transaction_type === 'return');

    const purchasesCount = purchases.length;
    const purchasesValue = purchases.reduce((sum, t) => sum + (t.total_price || 0), 0);

    const salesCount = sales.length;
    const salesValue = sales.reduce((sum, t) => sum + (t.total_price || 0), 0);
    const salesProfit = sales.reduce((sum, t) => sum + (t.profit || 0), 0);

    const returnsCount = returns.length;
    const returnsValue = returns.reduce((sum, t) => sum + (t.total_price || 0), 0);
    const returnsProfit = returns.reduce((sum, t) => sum + (t.profit || 0), 0);

    setTransactionStats({
      purchases: { count: purchasesCount, value: purchasesValue },
      sales: {
        count: salesCount - returnsCount,
        value: salesValue - returnsValue,
        profit: salesProfit - returnsProfit
      },
      returns: { count: returnsCount, value: returnsValue, profit: returnsProfit }
    });
  };

  // حساب البيانات الشهرية للمبيعات والأرباح
  const calculateMonthlyData = async (month = selectedMonth) => {
    try {
      // محاولة الحصول على البيانات من معالج get-monthly-charts-data
      if (window.api && window.api.invoke) {
        console.log(`جاري الحصول على بيانات الرسوم البيانية الشهرية من المعالج${month ? ` للشهر: ${month}` : ''}...`);

        // إعداد الفلاتر
        const filters = {};
        if (month) {
          filters.month = month;
        }

        const result = await window.api.invoke('get-monthly-charts-data', filters);

        if (result && result.success) {
          console.log('تم الحصول على بيانات الرسوم البيانية الشهرية بنجاح:', result);
          setMonthlySalesData(result.salesData || []);
          setMonthlyProfitData(result.profitData || []);
          return;
        } else {
          console.warn('فشل في الحصول على بيانات الرسوم البيانية الشهرية من المعالج:', result?.error);
        }
      }

      // استخدام الطريقة التقليدية كبديل
      console.log('استخدام الطريقة التقليدية لحساب البيانات الشهرية...');
      const monthlyData = {};
      const profitData = {};

      localTransactions.forEach(transaction => {
        const date = new Date(transaction.transaction_date);
        const monthYear = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

        if (!monthlyData[monthYear]) {
          monthlyData[monthYear] = {
            month: monthYear,
            label: formatMonthYear(date),
            sales: 0,
            purchases: 0,
            returns: 0,
            salesCount: 0,
            purchasesCount: 0,
            returnsCount: 0
          };
        }

        if (!profitData[monthYear]) {
          profitData[monthYear] = {
            month: monthYear,
            label: formatMonthYear(date),
            profit: 0,
            sales: 0,
            profitMargin: 0
          };
        }

        if (transaction.transaction_type === 'sale') {
          monthlyData[monthYear].sales += transaction.total_price || 0;
          monthlyData[monthYear].salesCount += 1;
          profitData[monthYear].profit += transaction.profit || 0;
          profitData[monthYear].sales += transaction.total_price || 0;
        } else if (transaction.transaction_type === 'purchase') {
          monthlyData[monthYear].purchases += transaction.total_price || 0;
          monthlyData[monthYear].purchasesCount += 1;
        } else if (transaction.transaction_type === 'return') {
          monthlyData[monthYear].returns += transaction.total_price || 0;
          monthlyData[monthYear].returnsCount += 1;
          profitData[monthYear].profit -= transaction.profit || 0;
          profitData[monthYear].sales -= transaction.total_price || 0;
        }
      });

      // حساب هامش الربح لكل شهر
      Object.values(profitData).forEach(month => {
        month.profitMargin = month.sales > 0 ? (month.profit / month.sales) * 100 : 0;
      });

      // تحويل البيانات إلى مصفوفة وترتيبها حسب التاريخ
      const sortedMonthlyData = Object.values(monthlyData).sort((a, b) => {
        return new Date(a.month) - new Date(b.month);
      });

      const sortedProfitData = Object.values(profitData).sort((a, b) => {
        return new Date(a.month) - new Date(b.month);
      });

      console.log('تم حساب البيانات الشهرية بنجاح:', {
        sales: sortedMonthlyData.length,
        profits: sortedProfitData.length
      });

      setMonthlySalesData(sortedMonthlyData);
      setMonthlyProfitData(sortedProfitData);
    } catch (error) {
      console.error('خطأ في حساب البيانات الشهرية:', error);
    }
  };

  // حساب الأصناف الأكثر ربحًا
  const calculateMostProfitableItems = () => {
    const sales = localTransactions.filter(t => t.transaction_type === 'sale');
    const itemSales = {};

    sales.forEach(sale => {
      if (!itemSales[sale.item_id]) {
        itemSales[sale.item_id] = {
          item_id: sale.item_id,
          item_name: sale.item_name,
          quantity: 0,
          value: 0,
          profit: 0
        };
      }

      itemSales[sale.item_id].quantity += sale.quantity;
      itemSales[sale.item_id].value += sale.total_price;
      itemSales[sale.item_id].profit += (sale.profit || 0);
    });

    // تحويل إلى مصفوفة وترتيب حسب الربح
    const profitableItems = Object.values(itemSales)
      .sort((a, b) => b.profit - a.profit)
      .slice(0, 5); // أعلى 5 أصناف

    setMostProfitableItems(profitableItems);
  };

  // تنسيق الشهر والسنة
  const formatMonthYear = (date) => {
    const months = ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    return `${months[date.getMonth()]} ${date.getFullYear()}`;
  };

  // حساب نسبة الربح
  const calculateProfitMarginLocal = (profit, sales) => {
    if (sales === 0) return 0;
    return (profit / sales) * 100;
  };

  // حفظ قيم الأرباح المحسوبة لتجنب إعادة الحساب المتكررة
  const [cachedProfitValues, setCachedProfitValues] = useState(profitValues);

  // تحديث القيم المحفوظة عند تغيير المعاملات المحلية
  useEffect(() => {
    if (localTransactions && localTransactions.length > 0) {
      const newProfitValues = calculateQuarterlyProfits(localTransactions);
      setCachedProfitValues(newProfitValues);
      console.log('تم تحديث قيم الأرباح المحفوظة:', newProfitValues);
    } else {
      // إذا لم تكن هناك معاملات، اعيد تعيين القيم إلى الصفر
      setCachedProfitValues({ quarterly: 0, halfYearly: 0, threeQuarters: 0, yearly: 0 });
    }
  }, [localTransactions, refreshKey]);

  // حساب أرباح الفترات المختلفة مع ضمان التحديث الفوري
  const calculateQuarterlyProfitsData = () => {
    // إذا لم تكن هناك معاملات محلية، استخدم القيم المحفوظة
    if (!localTransactions || localTransactions.length === 0) {
      return cachedProfitValues;
    }

    // إعادة حساب فوري للأرباح من المعاملات الحالية
    const freshProfitValues = calculateQuarterlyProfits(localTransactions);

    // تحديث القيم المحفوظة إذا كانت مختلفة
    if (JSON.stringify(freshProfitValues) !== JSON.stringify(cachedProfitValues)) {
      setCachedProfitValues(freshProfitValues);
      console.log('تم تحديث قيم الأرباح فورياً:', freshProfitValues);
    }

    return freshProfitValues;
  };

  return (
    <div className="financial-sales-report">
      {/* شريط التبويبات */}
      <div className="financial-tabs">
        <button
          className={`financial-tab ${activeTab === 'dashboard' ? 'active' : ''}`}
          onClick={() => setActiveTab('dashboard')}
        >
          <FaChartPie /> لوحة المعلومات
        </button>
        <button
          className={`financial-tab ${activeTab === 'transactions' ? 'active' : ''}`}
          onClick={() => setActiveTab('transactions')}
        >
          <FaChartBar /> المعاملات
        </button>
        <button
          className={`financial-tab ${activeTab === 'sales' ? 'active' : ''}`}
          onClick={() => setActiveTab('sales')}
        >
          <FaShoppingCart /> المبيعات
        </button>
        <button
          className={`financial-tab ${activeTab === 'purchases' ? 'active' : ''}`}
          onClick={() => setActiveTab('purchases')}
        >
          <FaArrowDown /> المشتريات
        </button>
        <button
          className={`financial-tab ${activeTab === 'transport' ? 'active' : ''}`}
          onClick={() => setActiveTab('transport')}
        >
          <FaTruck /> مصاريف النقل
        </button>
        <button
          className={`financial-tab ${activeTab === 'profits' ? 'active' : ''}`}
          onClick={() => setActiveTab('profits')}
        >
          <FaMoneyBillWave /> الأرباح
        </button>
        <button
          className={`financial-tab ${activeTab === 'analysis' ? 'active' : ''}`}
          onClick={() => setActiveTab('analysis')}
        >
          <FaChartLine /> التحليل
        </button>
      </div>

      {/* فلتر النطاق الزمني */}
      <div className="date-range-filter">
        <label>
          <FaCalendarAlt /> النطاق الزمني:
          <select
            value={dateRange}
            onChange={(e) => onDateRangeChange(e.target.value)}
          >
            <option value="all">جميع الفترات</option>
            <option value="quarter">الربع الحالي</option>
            <option value="halfYear">النصف سنوي</option>
            <option value="threeQuarters">ثلاثة أرباع السنة</option>
            <option value="year">السنة الحالية</option>
          </select>
        </label>
      </div>

      {/* محتوى التبويب النشط */}
      <div className="financial-content">
        {activeTab === 'dashboard' && (
          <div className="financial-dashboard">
            <h2 className="section-title">لوحة معلومات المبيعات والمالية</h2>

            {/* الإحصائيات الرئيسية */}
            <div className="stats-grid">
              <div className="stat-card sales-card">
                <div className="stat-card-icon">
                  <FaShoppingCart />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={transactionStats.sales.value} />
                </div>
                <div className="stat-card-title">إجمالي المبيعات</div>
                <div className="stat-card-subtitle">
                  {transactionStats.sales.count} عملية بيع
                </div>
              </div>

              <div className="stat-card profit-card">
                <div className="stat-card-icon">
                  <FaMoneyBillWave />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={transactionStats.sales.profit} isProfit={true} />
                </div>
                <div className="stat-card-title">إجمالي الأرباح</div>
                <div className="stat-card-subtitle">
                  نسبة الربح: {calculateProfitMarginLocal(transactionStats.sales.profit, transactionStats.sales.value).toFixed(1)}%
                </div>
              </div>

              <div className="stat-card purchases-card">
                <div className="stat-card-icon">
                  <FaArrowDown />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={transactionStats.purchases.value} />
                </div>
                <div className="stat-card-title">إجمالي المشتريات</div>
                <div className="stat-card-subtitle">
                  {transactionStats.purchases.count} عملية شراء
                </div>
              </div>
            </div>

            {/* الأصناف الأكثر ربحًا */}
            <div className="dashboard-section">
              <h3 className="section-subtitle">
                <FaBoxes /> الأصناف الأكثر ربحًا
              </h3>
              <DataTable
                columns={[
                  {
                    header: '#',
                    accessor: 'index',
                    cell: (_, index) => index + 1,
                    style: { width: '50px' }
                  },
                  {
                    header: 'الصنف',
                    accessor: 'item_name',
                    cell: (row) => row.item_name || 'غير معروف'
                  },
                  {
                    header: 'الكمية المباعة',
                    accessor: 'quantity',
                    cell: (row) => row.quantity || 0
                  },
                  {
                    header: 'قيمة المبيعات',
                    accessor: 'value',
                    cell: (row) => <FormattedCurrency amount={row.value || 0} />
                  },
                  {
                    header: 'الربح',
                    accessor: 'profit',
                    cell: (row) => (
                      <span className="text-success font-weight-bold">
                        <FormattedCurrency
                          amount={row.profit || 0}
                          isProfit={true}
                          useThousandSeparator={true}
                        />
                      </span>
                    )
                  },
                  {
                    header: 'نسبة الربح',
                    accessor: 'profit_percentage',
                    cell: (row) => (
                      <span className="font-weight-bold">
                        {row.value && row.profit ? ((row.profit / row.value) * 100).toFixed(2) : '0.00'}%
                      </span>
                    )
                  }
                ]}
                data={mostProfitableItems}
                pagination={false}
                searchable={false}
                emptyMessage="لا توجد بيانات للعرض"
              />
            </div>
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="financial-transactions">
            <h2 className="section-title">تقرير المعاملات</h2>

            {/* إحصائيات المعاملات */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaArrowDown />
                </div>
                <div className="stat-card-value">{transactionStats.purchases.count}</div>
                <div className="stat-card-title">عمليات الشراء</div>
                <div className="stat-card-subtitle">
                  <FormattedCurrency amount={transactionStats.purchases.value} />
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaArrowUp />
                </div>
                <div className="stat-card-value">{transactionStats.sales.count}</div>
                <div className="stat-card-title">عمليات البيع</div>
                <div className="stat-card-subtitle">
                  <FormattedCurrency amount={transactionStats.sales.value} />
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaUndo />
                </div>
                <div className="stat-card-value">{transactionStats.returns.count}</div>
                <div className="stat-card-title">عمليات الإرجاع</div>
                <div className="stat-card-subtitle">
                  <FormattedCurrency amount={transactionStats.returns.value} />
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaMoneyBillWave />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={transactionStats.sales.profit} isProfit={true} />
                </div>
                <div className="stat-card-title">إجمالي الأرباح</div>
                <div className="stat-card-subtitle">
                  من المبيعات
                </div>
              </div>
            </div>

            {/* جدول المعاملات */}
            <div className="dashboard-section">
              <h3 className="section-subtitle">
                <FaChartBar /> جميع المعاملات
              </h3>
              <DataTable
                columns={[
                  {
                    header: 'رقم المعاملة',
                    accessor: 'transaction_id',
                    cell: (row) => (
                      <span className="transaction-id">
                        {row.transaction_id || (row.id ? `TRX-${row.id.toString().padStart(6, '0')}` : 'TRX-000000')}
                      </span>
                    )
                  },
                  {
                    header: 'التاريخ',
                    accessor: 'transaction_date',
                    cell: (row) => formatDate(row.transaction_date)
                  },
                  {
                    header: 'نوع المعاملة',
                    accessor: 'transaction_type',
                    cell: (row) => {
                      switch (row.transaction_type) {
                        case 'purchase':
                          return 'شراء';
                        case 'sale':
                          return 'بيع';
                        case 'return':
                          return 'إرجاع';
                        default:
                          return row.transaction_type;
                      }
                    }
                  },
                  {
                    header: 'الصنف',
                    accessor: 'item_name',
                    cell: (row) => row.item_name
                  },
                  {
                    header: 'الكمية',
                    accessor: 'quantity',
                    cell: (row) => row.quantity
                  },
                  {
                    header: 'السعر',
                    accessor: 'price',
                    cell: (row) => row.price ? <FormattedCurrency amount={row.price} /> : '-'
                  },
                  {
                    header: 'الإجمالي',
                    accessor: 'total_price',
                    cell: (row) => row.total_price ? <FormattedCurrency amount={row.total_price} /> : '-'
                  },
                  {
                    header: 'الربح',
                    accessor: 'profit',
                    cell: (row) => {
                      if (row.transaction_type === 'purchase') return '-';
                      return row.profit ? (
                        <span className={row.transaction_type === 'return' ? 'text-danger' : 'text-success'}>
                          <FormattedCurrency amount={row.profit} isProfit={row.transaction_type !== 'return'} />
                        </span>
                      ) : '-';
                    }
                  }
                ]}
                data={localTransactions}
                pagination={true}
                pageSize={10}
                searchable={true}
                searchPlaceholder="بحث عن معاملة..."
                emptyMessage="لا توجد بيانات للعرض"
              />
            </div>
          </div>
        )}

        {activeTab === 'sales' && (
          <div className="financial-sales">
            <h2 className="section-title">تقرير المبيعات</h2>

            {/* إحصائيات المبيعات */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaShoppingCart />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={transactionStats.sales.value} />
                </div>
                <div className="stat-card-title">إجمالي المبيعات</div>
                <div className="stat-card-subtitle">
                  {transactionStats.sales.count} عملية بيع
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaUndo />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={transactionStats.returns.value} />
                </div>
                <div className="stat-card-title">إجمالي الإرجاعات</div>
                <div className="stat-card-subtitle">
                  {transactionStats.returns.count} عملية إرجاع
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaMoneyBillWave />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={transactionStats.sales.value - transactionStats.returns.value} />
                </div>
                <div className="stat-card-title">صافي المبيعات</div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaPercentage />
                </div>
                <div className="stat-card-value">
                  {calculateProfitMargin(transactionStats.sales.profit, transactionStats.sales.value).toFixed(1)}%
                </div>
                <div className="stat-card-title">نسبة الربح</div>
              </div>
            </div>

            {/* تحليل المبيعات الشهرية */}
            <div className="dashboard-section">
              <h3 className="section-subtitle">
                <FaChartBar /> تحليل المبيعات الشهرية
              </h3>
              <DataTable
                columns={[
                  {
                    header: 'الشهر',
                    accessor: 'label',
                    cell: (row) => row.label
                  },
                  {
                    header: 'المبيعات',
                    accessor: 'sales',
                    cell: (row) => <FormattedCurrency amount={row.sales} />
                  },
                  {
                    header: 'المشتريات',
                    accessor: 'purchases',
                    cell: (row) => <FormattedCurrency amount={row.purchases} />
                  },
                  {
                    header: 'الإرجاعات',
                    accessor: 'returns',
                    cell: (row) => <FormattedCurrency amount={row.returns} />
                  },
                  {
                    header: 'صافي المبيعات',
                    accessor: 'netSales',
                    cell: (row) => <FormattedCurrency amount={row.sales - row.returns} />
                  }
                ]}
                data={monthlySalesData}
                pagination={false}
                searchable={false}
                emptyMessage="لا توجد بيانات للعرض"
              />
            </div>
          </div>
        )}

        {activeTab === 'purchases' && (
          <div className="financial-purchases">
            <h2 className="section-title">تقرير المشتريات</h2>

            {/* إحصائيات المشتريات */}
            <div className="stats-grid">
              <div className="stat-card purchases-card">
                <div className="stat-card-icon">
                  <FaArrowDown />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={transactionStats.purchases.value} />
                </div>
                <div className="stat-card-title">إجمالي المشتريات</div>
                <div className="stat-card-subtitle">
                  {transactionStats.purchases.count} عملية شراء
                </div>
              </div>

              <div className="stat-card transport-card">
                <div className="stat-card-icon">
                  <FaTruck />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={cashboxReport?.cashbox?.transport_total || 0} />
                </div>
                <div className="stat-card-title">إجمالي مصاريف النقل</div>
                <div className="stat-card-subtitle">
                  مصاريف الشحن والنقل
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaMoneyBillWave />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={transactionStats.purchases.value / transactionStats.purchases.count} />
                </div>
                <div className="stat-card-title">متوسط قيمة المشتريات</div>
                <div className="stat-card-subtitle">
                  لكل عملية شراء
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaCalendarAlt />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={transactionStats.purchases.value / (monthlySalesData.length || 1)} />
                </div>
                <div className="stat-card-title">متوسط المشتريات الشهري</div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaExchangeAlt />
                </div>
                <div className="stat-card-value">
                  {transactionStats.sales.value > 0 ?
                    ((transactionStats.purchases.value / transactionStats.sales.value) * 100).toFixed(1) : '0'}%
                </div>
                <div className="stat-card-title">نسبة المشتريات للمبيعات</div>
              </div>
            </div>

            {/* جدول المشتريات */}
            <div className="dashboard-section">
              <h3 className="section-subtitle">
                <FaArrowDown /> عمليات الشراء
              </h3>
              <DataTable
                columns={[
                  {
                    header: 'رقم المعاملة',
                    accessor: 'transaction_id',
                    cell: (row) => (
                      <span className="transaction-id">
                        {row.transaction_id || (row.id ? `TRX-${row.id.toString().padStart(6, '0')}` : 'TRX-000000')}
                      </span>
                    )
                  },
                  {
                    header: 'رقم الفاتورة',
                    accessor: 'invoice_number',
                    cell: (row) => row.invoice_number || '-'
                  },
                  {
                    header: 'التاريخ',
                    accessor: 'transaction_date',
                    cell: (row) => formatDate(row.transaction_date)
                  },
                  {
                    header: 'الصنف',
                    accessor: 'item_name',
                    cell: (row) => row.item_name
                  },
                  {
                    header: 'الكمية',
                    accessor: 'quantity',
                    cell: (row) => row.quantity
                  },
                  {
                    header: 'سعر الوحدة',
                    accessor: 'price',
                    cell: (row) => row.price ? <FormattedCurrency amount={row.price} /> : '-'
                  },
                  {
                    header: 'الإجمالي',
                    accessor: 'total_price',
                    cell: (row) => row.total_price ? <FormattedCurrency amount={row.total_price} /> : '-'
                  }
                ]}
                data={transactions.filter(t => t.transaction_type === 'purchase')}
                pagination={true}
                pageSize={10}
                searchable={true}
                searchPlaceholder="بحث برقم الفاتورة..."
                searchFields={['invoice_number']}
                emptyMessage="لا توجد بيانات للعرض"
              />
            </div>

            {/* فواتير الشراء حسب الشهر */}
            <div className="dashboard-section">
              <h3 className="section-subtitle">
                <FaChartBar /> فواتير الشراء حسب الشهر
              </h3>
              <div className="month-filter-container">
                <select
                  className="month-filter"
                  value={purchasesMonthFilter}
                  onChange={handlePurchasesMonthFilterChange}
                >
                  <option value="">جميع الأشهر</option>
                  {monthlySalesData.map((month, index) => (
                    <option key={index} value={month.label}>{month.label}</option>
                  ))}
                </select>
              </div>
              <DataTable
                columns={[
                  {
                    header: 'الشهر',
                    accessor: 'label',
                    cell: (row) => row.label
                  },
                  {
                    header: 'عدد الفواتير',
                    accessor: 'invoiceCount',
                    cell: (row) => {
                      // حساب عدد الفواتير الفريدة في هذا الشهر
                      const monthDate = new Date(row.label);
                      const monthYear = `${monthDate.getFullYear()}-${(monthDate.getMonth() + 1).toString().padStart(2, '0')}`;

                      const invoices = transactions
                        .filter(t => t.transaction_type === 'purchase')
                        .filter(t => {
                          const date = new Date(t.transaction_date);
                          const transactionMonthYear = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
                          return transactionMonthYear === monthYear;
                        })
                        .map(t => t.invoice_number)
                        .filter((value, index, self) => value && self.indexOf(value) === index);

                      return invoices.length;
                    }
                  },
                  {
                    header: 'المشتريات',
                    accessor: 'purchases',
                    cell: (row) => <FormattedCurrency amount={row.purchases} />
                  },
                  {
                    header: 'المبيعات',
                    accessor: 'sales',
                    cell: (row) => <FormattedCurrency amount={row.sales} />
                  },
                  {
                    header: 'نسبة المشتريات للمبيعات',
                    accessor: 'ratio',
                    cell: (row) => row.sales > 0 ? ((row.purchases / row.sales) * 100).toFixed(1) + '%' : '0%'
                  }
                ]}
                data={filteredMonthlySalesData}
                pagination={false}
                searchable={false}
                emptyMessage="لا توجد بيانات للعرض"
              />
            </div>

            {/* تحليل المشتريات الشهرية */}
            <div className="dashboard-section">
              <h3 className="section-subtitle">
                <FaChartBar /> تحليل المشتريات الشهرية
              </h3>
              <DataTable
                columns={[
                  {
                    header: 'الشهر',
                    accessor: 'label',
                    cell: (row) => row.label
                  },
                  {
                    header: 'المشتريات',
                    accessor: 'purchases',
                    cell: (row) => <FormattedCurrency amount={row.purchases} />
                  },
                  {
                    header: 'المبيعات',
                    accessor: 'sales',
                    cell: (row) => <FormattedCurrency amount={row.sales} />
                  },
                  {
                    header: 'نسبة المشتريات للمبيعات',
                    accessor: 'ratio',
                    cell: (row) => row.sales > 0 ? ((row.purchases / row.sales) * 100).toFixed(1) + '%' : '0%'
                  }
                ]}
                data={filteredMonthlySalesData}
                pagination={false}
                searchable={false}
                emptyMessage="لا توجد بيانات للعرض"
              />
            </div>
          </div>
        )}

        {activeTab === 'transport' && (
          <div className="financial-transport">
            <h2 className="section-title">تقرير مصاريف النقل</h2>

            {/* إحصائيات مصاريف النقل */}
            <div className="stats-grid">
              <div className="stat-card transport-card">
                <div className="stat-card-icon">
                  <FaTruck />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={cashboxReport?.cashbox?.transport_total || 0} />
                </div>
                <div className="stat-card-title">إجمالي مصاريف النقل</div>
                <div className="stat-card-subtitle">
                  جميع مصاريف الشحن والنقل
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaPercentage />
                </div>
                <div className="stat-card-value">
                  {transactionStats.purchases.value > 0
                    ? ((cashboxReport?.cashbox?.transport_total || 0) / transactionStats.purchases.value * 100).toFixed(2)
                    : '0.00'
                  }%
                </div>
                <div className="stat-card-title">نسبة مصاريف النقل للمشتريات</div>
                <div className="stat-card-subtitle">
                  نسبة مصاريف النقل من إجمالي المشتريات
                </div>
              </div>
            </div>

            {/* جدول مصاريف النقل */}
            <div className="dashboard-section">
              <h3 className="section-subtitle">
                <FaTruck /> تفاصيل مصاريف النقل
              </h3>
              <DataTable
                columns={[
                  {
                    header: 'التاريخ',
                    accessor: 'transaction_date',
                    cell: (row) => formatDate(row.transaction_date)
                  },
                  {
                    header: 'المبلغ',
                    accessor: 'transport_cost',
                    cell: (row) => <FormattedCurrency amount={row.transport_cost || 0} />
                  },
                  {
                    header: 'رقم الفاتورة',
                    accessor: 'invoice_number',
                    cell: (row) => row.invoice_number || '-'
                  },
                  {
                    header: 'الصنف المرتبط',
                    accessor: 'item_name',
                    cell: (row) => row.item_name || '-'
                  },
                  {
                    header: 'ملاحظات',
                    accessor: 'notes',
                    cell: (row) => row.notes || '-'
                  }
                ]}
                data={transactions.filter(t => t.transaction_type === 'purchase' && t.transport_cost > 0)}
                pagination={true}
                pageSize={10}
                searchable={true}
                searchPlaceholder="بحث برقم الفاتورة..."
                searchFields={['invoice_number']}
                emptyMessage="لا توجد مصاريف نقل مسجلة"
              />
            </div>
          </div>
        )}

        {activeTab === 'profits' && (
          <div className="financial-profits">
            <h2 className="section-title">تقرير الأرباح</h2>

            {/* إحصائيات الأرباح */}
            <div className="stats-grid" key={`profits-${refreshKey}`}>
              <div className="stat-card profit-quarterly" key={`quarterly-${refreshKey}-${calculateQuarterlyProfitsData().quarterly}`}>
                <div className="stat-card-icon">
                  <FaChartLine />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={calculateQuarterlyProfitsData().quarterly} isProfit={true} />
                </div>
                <div className="stat-card-title">الربح الربع الأول</div>
                <div className="stat-card-subtitle">يناير - مارس</div>
              </div>

              <div className="stat-card profit-half-yearly" key={`halfYearly-${refreshKey}-${calculateQuarterlyProfitsData().halfYearly}`}>
                <div className="stat-card-icon">
                  <FaChartLine />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={calculateQuarterlyProfitsData().halfYearly} isProfit={true} />
                </div>
                <div className="stat-card-title">الربح النصف سنوي</div>
                <div className="stat-card-subtitle">يناير - يونيو</div>
              </div>

              <div className="stat-card profit-three-quarters" key={`threeQuarters-${refreshKey}-${calculateQuarterlyProfitsData().threeQuarters}`}>
                <div className="stat-card-icon">
                  <FaChartLine />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={calculateQuarterlyProfitsData().threeQuarters} isProfit={true} />
                </div>
                <div className="stat-card-title">الربح ثلاثة أرباع سنوي</div>
                <div className="stat-card-subtitle">يناير - سبتمبر</div>
              </div>

              <div className="stat-card profit-yearly" key={`yearly-${refreshKey}-${calculateQuarterlyProfitsData().yearly}`}>
                <div className="stat-card-icon">
                  <FaChartLine />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={calculateQuarterlyProfitsData().yearly} isProfit={true} />
                </div>
                <div className="stat-card-title">الربح السنوي</div>
                <div className="stat-card-subtitle">يناير - ديسمبر</div>
              </div>
            </div>

            {/* توزيع الأرباح */}
            <div className="dashboard-section" key={`distribution-${refreshKey}`}>
              <h3 className="section-subtitle">
                <FaPercentage /> توزيع الأرباح السنوية
              </h3>

              <div className="profit-distribution">
                <div className="profit-distribution-item investment-item" style={{ flex: '3' }}>
                  <div className="profit-distribution-label">30% (الاستثمار)</div>
                  <div className="profit-distribution-value">
                    <FormattedCurrency
                      amount={Math.max(0, calculateQuarterlyProfitsData().yearly) * 0.3}
                      isProfit={true}
                      useThousandSeparator={true}
                      decimalPlaces={0}
                      className="investment-value"
                    />
                  </div>
                </div>
                <div className="profit-distribution-item profit-item" style={{ flex: '7' }}>
                  <div className="profit-distribution-label">70% (الأرباح)</div>
                  <div className="profit-distribution-value">
                    <FormattedCurrency
                      amount={Math.max(0, calculateQuarterlyProfitsData().yearly) * 0.7}
                      isProfit={true}
                      useThousandSeparator={true}
                      decimalPlaces={0}
                      className="profit-value"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* تحليل الأرباح الشهرية */}
            <div className="dashboard-section profit-analysis-section">
              <h3 className="section-subtitle">
                <FaChartBar /> تحليل الأرباح الشهرية
              </h3>
              <DataTable
                columns={[
                  {
                    header: 'الشهر',
                    accessor: 'label',
                    cell: (row) => <strong>{row.label}</strong>,
                    style: { width: '150px' }
                  },
                  {
                    header: 'الإيرادات',
                    accessor: 'revenue',
                    cell: (row) => (
                      <div className="monthly-data-cell">
                        <FormattedCurrency amount={row.revenue} />
                      </div>
                    )
                  },
                  {
                    header: 'الأرباح',
                    accessor: 'profit',
                    cell: (row) => (
                      <div className="monthly-data-cell profit-cell">
                        <span className="text-success font-weight-bold">
                          <FormattedCurrency amount={row.profit} isProfit={true} />
                        </span>
                      </div>
                    )
                  },
                  {
                    header: 'نسبة الربح',
                    accessor: 'profitMargin',
                    cell: (row) => (
                      <div className="monthly-data-cell profit-percentage-cell">
                        <span className="font-weight-bold">
                          {row.revenue > 0 ? ((row.profit / row.revenue) * 100).toFixed(2) : '0.00'}%
                        </span>
                      </div>
                    )
                  }
                ]}
                data={monthlyProfitData}
                pagination={false}
                searchable={false}
                emptyMessage="لا توجد بيانات للعرض"
              />
            </div>
          </div>
        )}



        {activeTab === 'analysis' && (
          <div className="financial-analysis">
            <h2 className="section-title">تحليل المبيعات والأرباح</h2>

            <div className="analysis-actions">
              <div className="month-filter">
                <label htmlFor="month-select">تصفية حسب الشهر: </label>
                <select
                  id="month-select"
                  value={selectedMonth}
                  onChange={handleMonthChange}
                  className="month-select"
                >
                  <option value="">جميع الأشهر</option>
                  {getAvailableMonths().map(month => (
                    <option key={month.value} value={month.value}>
                      {month.label}
                    </option>
                  ))}
                </select>
              </div>
              <button className="refresh-button" onClick={refreshMonthlyData}>
                <FaSync /> تحديث البيانات
              </button>
            </div>

            <div className="analysis-grid">
              <div className="analysis-card">
                <div className="analysis-card-header">
                  <div className="analysis-card-title">
                    <FaChartLine /> تطور المبيعات الشهرية
                  </div>
                </div>
                <div className="analysis-card-content">
                  {monthlySalesData && monthlySalesData.length > 0 ? (
                    <MonthlySalesChart
                      data={{
                        labels: monthlySalesData.map(item => item.label),
                        sales: monthlySalesData.map(item => item.sales),
                        purchases: monthlySalesData.map(item => item.purchases)
                      }}
                    />
                  ) : (
                    <div className="chart-placeholder">
                      <FaChartLine size={48} />
                      <p>لا توجد بيانات كافية لعرض الرسم البياني</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="analysis-card">
                <div className="analysis-card-header">
                  <div className="analysis-card-title">
                    <FaChartLine /> تطور الأرباح الشهرية
                  </div>
                </div>
                <div className="analysis-card-content">
                  {monthlyProfitData && monthlyProfitData.length > 0 ? (
                    <MonthlyProfitChart
                      data={{
                        labels: monthlyProfitData.map(item => item.label),
                        profit: monthlyProfitData.map(item => item.profit),
                        profitMargin: monthlyProfitData.map(item => item.profitMargin)
                      }}
                    />
                  ) : (
                    <div className="chart-placeholder">
                      <FaChartLine size={48} />
                      <p>لا توجد بيانات كافية لعرض الرسم البياني</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="dashboard-section">
              <h3 className="section-subtitle">
                <FaInfoCircle /> ملخص التحليل
              </h3>
              <div className="analysis-summary">
                <p>
                  <strong>متوسط المبيعات الشهرية:</strong> <FormattedCurrency amount={monthlySalesData.reduce((sum, month) => sum + month.sales, 0) / (monthlySalesData.length || 1)} />
                </p>
                <p>
                  <strong>متوسط الأرباح الشهرية:</strong> <FormattedCurrency amount={monthlyProfitData.reduce((sum, month) => sum + month.profit, 0) / (monthlyProfitData.length || 1)} isProfit={true} />
                </p>
                <p>
                  <strong>متوسط نسبة الربح:</strong> {calculateProfitMarginLocal(transactionStats.sales.profit, transactionStats.sales.value).toFixed(2)}%
                </p>
                <p>
                  <strong>توقعات الربح السنوي:</strong> <FormattedCurrency amount={calculateQuarterlyProfitsData().yearly * 1.1} isProfit={true} />
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FinancialSalesReport;
