/**
 * وظائف حساب الربح الموحدة
 * هذا الملف يحتوي على وظائف موحدة لحساب الربح في جميع أنحاء التطبيق
 */

/**
 * حساب الربح لمعاملة بيع
 * @param {number} sellingPrice - سعر البيع
 * @param {number} avgPrice - متوسط سعر الشراء
 * @param {number} quantity - الكمية
 * @returns {number} - الربح المحسوب
 */
export const calculateProfit = (sellingPrice, avgPrice, quantity) => {
  try {
    // التأكد من أن جميع المدخلات أرقام صالحة
    const numSellingPrice = Number(sellingPrice) || 0;
    const numAvgPrice = Number(avgPrice) || 0;
    const numQuantity = Number(quantity) || 0;

    console.log(`[PROFIT-CALC] حساب الربح - سعر البيع: ${numSellingPrice}, متوسط سعر الشراء: ${numAvgPrice}, الكمية: ${numQuantity}`);

    // التأكد من أن متوسط سعر الشراء متوفر وغير صفري
    if (numAvgPrice <= 0) {
      console.warn('[PROFIT-CALC] متوسط سعر الشراء غير متوفر أو صفري، سيتم استخدام 50% من سعر البيع كتكلفة تقديرية');
      // استخدام 50% من سعر البيع كتكلفة تقديرية إذا كان متوسط سعر الشراء غير متوفر
      const estimatedProfit = numSellingPrice * numQuantity * 0.5;
      console.log(`[PROFIT-CALC] الربح التقديري: ${estimatedProfit}`);
      return estimatedProfit;
    }

    // حساب الربح: (سعر البيع - متوسط سعر الشراء) * الكمية
    const profit = (numSellingPrice - numAvgPrice) * numQuantity;

    // التأكد من أن الربح لا يكون سالباً (في حالة كان سعر البيع أقل من سعر الشراء)
    const finalProfit = Math.max(0, profit);

    console.log(`[PROFIT-CALC] الربح المحسوب: (${numSellingPrice} - ${numAvgPrice}) × ${numQuantity} = ${profit}, الربح النهائي: ${finalProfit}`);

    return finalProfit;
  } catch (error) {
    console.error('[PROFIT-CALC] خطأ في حساب الربح:', error);
    return 0;
  }
};

/**
 * حساب الربح مع أخذ مصاريف النقل في الاعتبار
 * @param {number} sellingPrice - سعر البيع
 * @param {number} avgPrice - متوسط سعر الشراء
 * @param {number} quantity - الكمية
 * @param {number} transportCostPerUnit - مصاريف النقل لكل وحدة
 * @returns {number} - الربح المحسوب مع خصم مصاريف النقل
 */
export const calculateProfitWithTransport = (sellingPrice, avgPrice, quantity, transportCostPerUnit = 0) => {
  try {
    // التأكد من أن جميع المدخلات أرقام صالحة
    const numSellingPrice = Number(sellingPrice) || 0;
    const numAvgPrice = Number(avgPrice) || 0;
    const numQuantity = Number(quantity) || 0;
    const numTransportCostPerUnit = Number(transportCostPerUnit) || 0;

    console.log(`[PROFIT-CALC-TRANSPORT] حساب الربح مع مصاريف النقل:`);
    console.log(`[PROFIT-CALC-TRANSPORT] - سعر البيع: ${numSellingPrice}`);
    console.log(`[PROFIT-CALC-TRANSPORT] - متوسط سعر الشراء: ${numAvgPrice}`);
    console.log(`[PROFIT-CALC-TRANSPORT] - مصاريف النقل لكل وحدة: ${numTransportCostPerUnit}`);
    console.log(`[PROFIT-CALC-TRANSPORT] - الكمية: ${numQuantity}`);

    // التأكد من أن متوسط سعر الشراء متوفر وغير صفري
    if (numAvgPrice <= 0) {
      console.warn('[PROFIT-CALC-TRANSPORT] متوسط سعر الشراء غير متوفر أو صفري، سيتم استخدام 50% من سعر البيع كتكلفة تقديرية');
      // استخدام 50% من سعر البيع كتكلفة تقديرية إذا كان متوسط سعر الشراء غير متوفر
      // مع خصم مصاريف النقل
      const estimatedProfit = Math.max(0, (numSellingPrice * numQuantity * 0.5) - (numTransportCostPerUnit * numQuantity));
      console.log(`[PROFIT-CALC-TRANSPORT] الربح التقديري مع خصم مصاريف النقل: ${estimatedProfit}`);
      return estimatedProfit;
    }

    // حساب الربح: (سعر البيع - متوسط سعر الشراء - مصاريف النقل لكل وحدة) * الكمية
    const profit = (numSellingPrice - numAvgPrice - numTransportCostPerUnit) * numQuantity;

    // التأكد من أن الربح لا يكون سالباً
    const finalProfit = Math.max(0, profit);

    console.log(`[PROFIT-CALC-TRANSPORT] - الربح المحسوب: (${numSellingPrice} - ${numAvgPrice} - ${numTransportCostPerUnit}) × ${numQuantity} = ${profit}`);
    console.log(`[PROFIT-CALC-TRANSPORT] - الربح النهائي: ${finalProfit}`);

    return Math.round(finalProfit * 100) / 100; // تقريب إلى رقمين عشريين
  } catch (error) {
    console.error('[PROFIT-CALC-TRANSPORT] خطأ في حساب الربح مع مصاريف النقل:', error);
    return 0;
  }
};

/**
 * حساب نسبة الربح
 * @param {number} profit - الربح
 * @param {number} totalSales - إجمالي المبيعات
 * @returns {number} - نسبة الربح
 */
export const calculateProfitMargin = (profit, totalSales) => {
  // التأكد من أن المدخلات أرقام صالحة
  const numProfit = Number(profit) || 0;
  const numTotalSales = Number(totalSales) || 0;

  // تجنب القسمة على صفر
  if (numTotalSales <= 0) {
    return 0;
  }

  // حساب نسبة الربح
  return (numProfit / numTotalSales) * 100;
};

/**
 * حساب الربح للفترات الزمنية المختلفة
 * @param {Array} transactions - المعاملات
 * @param {Object} periods - الفترات الزمنية
 * @returns {Object} - الأرباح لكل فترة
 */
export const calculatePeriodProfits = (transactions, periods) => {
  if (!transactions || !Array.isArray(transactions) || transactions.length === 0) {
    return {};
  }

  if (!periods || typeof periods !== 'object') {
    return {};
  }

  // إنشاء كائن لتخزين الأرباح لكل فترة
  const periodProfits = {};

  // تهيئة الكائن بالفترات المطلوبة
  for (const [periodKey, period] of Object.entries(periods)) {
    periodProfits[periodKey] = {
      totalSales: 0,
      totalProfit: 0,
      transactionCount: 0,
      profitMargin: 0
    };
  }

  // حساب الإحصائيات لكل فترة
  for (const transaction of transactions) {
    // التأكد من أن المعاملة تحتوي على تاريخ صالح
    if (!transaction.transaction_date) continue;

    const transactionDate = new Date(transaction.transaction_date);

    for (const [periodKey, period] of Object.entries(periods)) {
      const periodStart = new Date(period.start);
      const periodEnd = new Date(period.end);

      if (transactionDate >= periodStart && transactionDate <= periodEnd) {
        periodProfits[periodKey].totalSales += transaction.total_price || 0;
        periodProfits[periodKey].totalProfit += transaction.profit || 0;
        periodProfits[periodKey].transactionCount++;
      }
    }
  }

  // حساب نسبة الربح لكل فترة
  for (const [periodKey, stats] of Object.entries(periodProfits)) {
    if (stats.totalSales > 0) {
      stats.profitMargin = calculateProfitMargin(stats.totalProfit, stats.totalSales);
    }
  }

  return periodProfits;
};

/**
 * حساب الأرباح للفترات الزمنية المختلفة (الربع الأول، الثاني، الثالث، والسنة الكاملة)
 * @param {Array} transactions - المعاملات
 * @returns {Object} - الأرباح للفترات المختلفة
 */
export const calculateQuarterlyProfits = (transactions) => {
  if (!transactions || !Array.isArray(transactions) || transactions.length === 0) {
    return {
      quarterly: 0,
      halfYearly: 0,
      threeQuarters: 0,
      yearly: 0
    };
  }

  // الحصول على السنة الحالية
  const currentYear = new Date().getFullYear();

  // تصفية معاملات البيع والإرجاع للسنة الحالية
  const salesTransactions = transactions.filter(t =>
    t.transaction_type === 'sale' &&
    new Date(t.transaction_date).getFullYear() === currentYear
  );

  const returnTransactions = transactions.filter(t =>
    t.transaction_type === 'return' &&
    new Date(t.transaction_date).getFullYear() === currentYear
  );

  // تحديد تواريخ الأرباع
  const q1Start = new Date(currentYear, 0, 1); // 1 يناير
  const q1End = new Date(currentYear, 2, 31); // 31 مارس

  const q2Start = new Date(currentYear, 3, 1); // 1 أبريل
  const q2End = new Date(currentYear, 5, 30); // 30 يونيو

  const q3Start = new Date(currentYear, 6, 1); // 1 يوليو
  const q3End = new Date(currentYear, 8, 30); // 30 سبتمبر

  const q4Start = new Date(currentYear, 9, 1); // 1 أكتوبر
  const q4End = new Date(currentYear, 11, 31); // 31 ديسمبر

  // حساب أرباح كل ربع (المبيعات - الإرجاعات)
  const q1SalesProfit = salesTransactions
    .filter(t => {
      const date = new Date(t.transaction_date);
      return date >= q1Start && date <= q1End;
    })
    .reduce((sum, t) => sum + (t.profit || 0), 0);

  const q1ReturnsProfit = returnTransactions
    .filter(t => {
      const date = new Date(t.transaction_date);
      return date >= q1Start && date <= q1End;
    })
    .reduce((sum, t) => sum + (t.profit || 0), 0);

  const q1Profit = Math.max(0, q1SalesProfit - q1ReturnsProfit);

  const q2SalesProfit = salesTransactions
    .filter(t => {
      const date = new Date(t.transaction_date);
      return date >= q2Start && date <= q2End;
    })
    .reduce((sum, t) => sum + (t.profit || 0), 0);

  const q2ReturnsProfit = returnTransactions
    .filter(t => {
      const date = new Date(t.transaction_date);
      return date >= q2Start && date <= q2End;
    })
    .reduce((sum, t) => sum + (t.profit || 0), 0);

  const q2Profit = Math.max(0, q2SalesProfit - q2ReturnsProfit);

  const q3SalesProfit = salesTransactions
    .filter(t => {
      const date = new Date(t.transaction_date);
      return date >= q3Start && date <= q3End;
    })
    .reduce((sum, t) => sum + (t.profit || 0), 0);

  const q3ReturnsProfit = returnTransactions
    .filter(t => {
      const date = new Date(t.transaction_date);
      return date >= q3Start && date <= q3End;
    })
    .reduce((sum, t) => sum + (t.profit || 0), 0);

  const q3Profit = Math.max(0, q3SalesProfit - q3ReturnsProfit);

  const q4SalesProfit = salesTransactions
    .filter(t => {
      const date = new Date(t.transaction_date);
      return date >= q4Start && date <= q4End;
    })
    .reduce((sum, t) => sum + (t.profit || 0), 0);

  const q4ReturnsProfit = returnTransactions
    .filter(t => {
      const date = new Date(t.transaction_date);
      return date >= q4Start && date <= q4End;
    })
    .reduce((sum, t) => sum + (t.profit || 0), 0);

  const q4Profit = Math.max(0, q4SalesProfit - q4ReturnsProfit);

  // حساب الأرباح التراكمية
  const yearlyProfit = q1Profit + q2Profit + q3Profit + q4Profit;
  const halfYearlyProfit = q1Profit + q2Profit;
  const threeQuartersProfit = q1Profit + q2Profit + q3Profit;

  // تسجيل للتشخيص
  console.log('[PROFIT-CALC] حساب الأرباح الربع سنوية:', {
    q1: q1Profit,
    q2: q2Profit,
    q3: q3Profit,
    q4: q4Profit,
    quarterly: q1Profit,
    halfYearly: halfYearlyProfit,
    threeQuarters: threeQuartersProfit,
    yearly: yearlyProfit,
    salesCount: salesTransactions.length,
    returnsCount: returnTransactions.length
  });

  return {
    quarterly: q1Profit,
    halfYearly: halfYearlyProfit,
    threeQuarters: threeQuartersProfit,
    yearly: yearlyProfit
  };
};

export default {
  calculateProfit,
  calculateProfitWithTransport,
  calculateProfitMargin,
  calculatePeriodProfits,
  calculateQuarterlyProfits
};
